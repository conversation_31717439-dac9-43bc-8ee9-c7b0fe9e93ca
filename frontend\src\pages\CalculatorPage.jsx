import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import LoadingSpinner from '../components/LoadingSpinner';

const CalculatorPage = () => {
  const { itemId } = useParams();
  const navigate = useNavigate();
  const [item, setItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Calculator states
  const [weight, setWeight] = useState('');
  const [price, setPrice] = useState('');
  const [calculatedPrice, setCalculatedPrice] = useState('');
  const [calculatedWeight, setCalculatedWeight] = useState('');

  useEffect(() => {
    fetchItem();
  }, [itemId]);

  const fetchItem = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/items');
      
      if (response.data.success) {
        const foundItem = response.data.data.find(item => item.id === itemId);
        if (foundItem) {
          setItem(foundItem);
        } else {
          setError('Product not found');
        }
      }
    } catch (error) {
      console.error('Error fetching item:', error);
      setError('Failed to load product');
    } finally {
      setLoading(false);
    }
  };

  const handleWeightChange = (e) => {
    const weightValue = e.target.value;
    setWeight(weightValue);
    setPrice(''); // Clear price input
    
    if (weightValue && item) {
      const weightInKg = parseFloat(weightValue) / 1000;
      const totalPrice = (weightInKg * item.pricePerKg).toFixed(2);
      setCalculatedPrice(totalPrice);
      setCalculatedWeight('');
    } else {
      setCalculatedPrice('');
    }
  };

  const handlePriceChange = (e) => {
    const priceValue = e.target.value;
    setPrice(priceValue);
    setWeight(''); // Clear weight input
    
    if (priceValue && item) {
      const weightInGrams = (parseFloat(priceValue) / item.pricePerKg) * 1000;
      setCalculatedWeight(weightInGrams);
      setCalculatedPrice('');
    } else {
      setCalculatedWeight('');
    }
  };

  const formatWeight = (weightInGrams) => {
    if (weightInGrams >= 1000) {
      return `${(weightInGrams / 1000).toFixed(2)} Kg`;
    }
    return `${Math.round(weightInGrams)} g`;
  };

  const clearCalculator = () => {
    setWeight('');
    setPrice('');
    setCalculatedPrice('');
    setCalculatedWeight('');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-main flex items-center justify-center">
        <LoadingSpinner size="large" text="Loading calculator..." />
      </div>
    );
  }

  if (error || !item) {
    return (
      <div className="min-h-screen bg-gradient-main flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{error}</h2>
          <button
            onClick={() => navigate('/')}
            className="px-6 py-3 bg-gradient-emerald text-white rounded-xl hover:shadow-emerald transition-all duration-300 font-semibold"
          >
            Back to Home
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-main">
      {/* Header */}
      <motion.header
        className="bg-white/80 backdrop-blur-sm shadow-card sticky top-0 z-50"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate('/')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="font-medium">Back to Store</span>
            </button>
            <h1 className="text-2xl font-bold text-gray-900">Price Calculator</h1>
            <div className="w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        <motion.div
          className="bg-white rounded-3xl shadow-soft overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Product Header */}
          <div className="bg-gradient-card p-8 border-b border-gray-100">
            <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
              <motion.div
                className="w-48 h-48 rounded-2xl overflow-hidden shadow-card"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <img
                  src={`http://localhost:5000${item.image}`}
                  alt={item.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDMTA4LjI4NCA3MCA5NS4wNzE2IDc2LjE5MjYgMTAwIDg0LjQ3NzZDMTA0LjkyOCA3Ni4xOTI2IDExMS43MTYgNzAgMTIwIDcwQzEyOC4yODQgNzAgMTM1IDc2LjcxNiAxMzUgODVDMTM1IDkzLjI4NCAxMjguMjg0IDEwMCAxMjAgMTAwSDgwQzcxLjcxNiAxMDAgNjUgOTMuMjg0IDY1IDg1QzY1IDc2LjcxNiA3MS43MTYgNzAgODAgNzBIMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                  }}
                />
              </motion.div>
              <div className="text-center md:text-left">
                <h2 className="text-4xl font-bold text-gray-900 mb-4 capitalize">{item.name}</h2>
                <div className="flex items-center justify-center md:justify-start space-x-4 mb-4">
                  <span className="px-4 py-2 bg-gradient-emerald text-white rounded-full font-bold text-lg">
                    ₹{item.pricePerKg}/kg
                  </span>
                  <span className="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium">
                    Fresh Quality
                  </span>
                </div>
                <p className="text-gray-600 text-lg">
                  Calculate the exact price for any quantity or find out how much you can get for your budget.
                </p>
              </div>
            </div>
          </div>

          {/* Calculator Section */}
          <div className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Weight to Price Calculator */}
              <motion.div
                className="bg-gradient-to-br from-emerald-50 to-emerald-100 p-6 rounded-2xl border-2 border-emerald-200"
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-12 h-12 bg-gradient-emerald rounded-xl flex items-center justify-center text-white">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-1m-3 1l-3-1" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-emerald-900">Weight → Price</h3>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-semibold text-emerald-800 mb-2">
                      Enter weight (grams):
                    </label>
                    <input
                      type="number"
                      value={weight}
                      onChange={handleWeightChange}
                      placeholder="e.g., 250"
                      min="0"
                      step="1"
                      className="w-full px-4 py-3 border-2 border-emerald-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white text-lg"
                    />
                  </div>
                  
                  <div className="bg-white rounded-xl p-4 border border-emerald-200">
                    <div className="text-center">
                      <p className="text-sm text-emerald-700 mb-1">Total Price</p>
                      <p className="text-3xl font-bold text-emerald-900">
                        ₹{calculatedPrice || '0.00'}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Price to Weight Calculator */}
              <motion.div
                className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-2xl border-2 border-blue-200"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-12 h-12 bg-gradient-blue rounded-xl flex items-center justify-center text-white">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-blue-900">Price → Weight</h3>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-semibold text-blue-800 mb-2">
                      Enter price (₹):
                    </label>
                    <input
                      type="number"
                      value={price}
                      onChange={handlePriceChange}
                      placeholder="e.g., 25"
                      min="0"
                      step="0.01"
                      className="w-full px-4 py-3 border-2 border-blue-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-lg"
                    />
                  </div>
                  
                  <div className="bg-white rounded-xl p-4 border border-blue-200">
                    <div className="text-center">
                      <p className="text-sm text-blue-700 mb-1">You Get</p>
                      <p className="text-3xl font-bold text-blue-900">
                        {calculatedWeight ? formatWeight(calculatedWeight) : '0 g'}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Clear Button */}
            <motion.div
              className="text-center mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <button
                onClick={clearCalculator}
                className="px-8 py-3 bg-gradient-purple text-white rounded-xl hover:shadow-purple transition-all duration-300 font-semibold"
              >
                Clear Calculator
              </button>
            </motion.div>
          </div>
        </motion.div>
      </main>
    </div>
  );
};

export default CalculatorPage;
