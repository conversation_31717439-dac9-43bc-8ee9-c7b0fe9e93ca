{"dependencies": {"@tailwindcss/postcss": "^4.1.12", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "express": "^4.21.2", "framer-motion": "^12.23.12", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.1", "tailwindcss": "^4.1.12", "vite": "^7.1.2"}, "name": "groceries-store-with-calculator", "version": "1.0.0", "description": "A full-stack web application for shopkeepers to manage items and calculate prices/weights instantly.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC"}