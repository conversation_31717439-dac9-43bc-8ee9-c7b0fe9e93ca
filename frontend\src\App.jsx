import React, { useState } from 'react';
import ItemForm from './components/ItemForm';
import ItemList from './components/ItemList';

function App() {
  const [refreshItems, setRefreshItems] = useState(0);

  const handleItemAdded = () => {
    // Trigger refresh of item list
    setRefreshItems(prev => prev + 1);
  };

  const handleItemDeleted = () => {
    // Trigger refresh of item list
    setRefreshItems(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Shopkeeper Calculator
              </h1>
              <p className="text-gray-600 mt-1">
                Manage items and calculate prices instantly
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Add Item Form */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Add New Item
              </h2>
              <ItemForm onItemAdded={handleItemAdded} />
            </div>
          </div>

          {/* Items List */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Items & Calculator
              </h2>
              <ItemList 
                refreshTrigger={refreshItems} 
                onItemDeleted={handleItemDeleted}
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
