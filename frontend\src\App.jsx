import React, { useState } from 'react';
import Logo from './components/Logo';
import CustomerView from './components/CustomerView';
import AdminPanel from './components/AdminPanel';

function App() {
  const [refreshItems, setRefreshItems] = useState(0);
  const [currentView, setCurrentView] = useState('customer'); // 'customer' or 'admin'

  const handleItemAdded = () => {
    // Trigger refresh of item list
    setRefreshItems(prev => prev + 1);
  };

  const handleItemDeleted = () => {
    // Trigger refresh of item list
    setRefreshItems(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-lg border-b border-green-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Logo />
              <div className="hidden sm:block">
                <h1 className="text-2xl font-bold text-gray-900">
                  Inventory Management System
                </h1>
                <p className="text-green-600 text-sm font-medium">
                  Professional grocery management solution
                </p>
              </div>
            </div>

            {/* View Toggle */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentView('customer')}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  currentView === 'customer'
                    ? 'bg-green-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Customer View
              </button>
              <button
                onClick={() => setCurrentView('admin')}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  currentView === 'admin'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Admin Panel
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'customer' ? (
          <CustomerView
            refreshTrigger={refreshItems}
            onItemDeleted={handleItemDeleted}
          />
        ) : (
          <AdminPanel
            refreshTrigger={refreshItems}
            onItemAdded={handleItemAdded}
            onItemDeleted={handleItemDeleted}
          />
        )}
      </main>
    </div>
  );
}

export default App;
