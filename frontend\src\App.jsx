import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Navigation from './components/Navigation';
import HeroSection from './components/HeroSection';
import HowToUseSection from './components/HowToUseSection';
import CustomerView from './components/CustomerView';
import AdminPanel from './components/AdminPanel';
import Footer from './components/Footer';
import CalculatorPage from './pages/CalculatorPage';

function App() {
  const [refreshItems, setRefreshItems] = useState(0);
  const [currentView, setCurrentView] = useState('customer'); // 'customer' or 'admin'

  const handleItemAdded = () => {
    // Trigger refresh of item list
    setRefreshItems(prev => prev + 1);
  };

  const handleItemDeleted = () => {
    // Trigger refresh of item list
    setRefreshItems(prev => prev + 1);
  };

  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 }
  };

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.5
  };

  const HomePage = () => (
    <div className="min-h-screen bg-gradient-main">
      {/* Navigation */}
      <Navigation currentView={currentView} setCurrentView={setCurrentView} />

      {/* Hero Section - Only show on customer view */}
      <AnimatePresence mode="wait">
        {currentView === 'customer' && (
          <motion.div
            key="hero"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <HeroSection />
          </motion.div>
        )}
      </AnimatePresence>

      {/* How to Use Section - Only show on customer view */}
      <AnimatePresence mode="wait">
        {currentView === 'customer' && (
          <motion.div
            key="howto"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ ...pageTransition, delay: 0.2 }}
          >
            <HowToUseSection />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <motion.main
        className={`${currentView === 'customer' ? 'pt-0' : 'pt-24'} pb-20`}
        initial="initial"
        animate="in"
        variants={pageVariants}
        transition={pageTransition}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatePresence mode="wait">
            {currentView === 'customer' ? (
              <motion.div
                key="customer"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <CustomerView
                  refreshTrigger={refreshItems}
                  onItemDeleted={handleItemDeleted}
                />
              </motion.div>
            ) : (
              <motion.div
                key="admin"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <AdminPanel
                  refreshTrigger={refreshItems}
                  onItemAdded={handleItemAdded}
                  onItemDeleted={handleItemDeleted}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.main>

      {/* Footer - Only show on customer view */}
      <AnimatePresence mode="wait">
        {currentView === 'customer' && (
          <motion.div
            key="footer"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={{ ...pageTransition, delay: 0.4 }}
          >
            <Footer />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/calculator/:itemId" element={<CalculatorPage />} />
      </Routes>
    </Router>
  );
}

export default App;
