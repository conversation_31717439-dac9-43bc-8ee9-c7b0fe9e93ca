import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ItemCard from './ItemCard';

const ItemList = ({ refreshTrigger, onItemDeleted }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch items from API
  const fetchItems = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await axios.get('/api/items');
      
      if (response.data.success) {
        setItems(response.data.data);
      } else {
        setError('Failed to fetch items');
      }
    } catch (error) {
      console.error('Error fetching items:', error);
      setError(
        error.response?.data?.message || 
        error.response?.data?.error || 
        'Failed to load items. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch items on component mount and when refreshTrigger changes
  useEffect(() => {
    fetchItems();
  }, [refreshTrigger]);

  // Handle item deletion
  const handleItemDeleted = () => {
    fetchItems(); // Refresh the list
    if (onItemDeleted) {
      onItemDeleted();
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading items...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md inline-block">
          {error}
        </div>
        <div className="mt-4">
          <button
            onClick={fetchItems}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (items.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No items yet</h3>
        <p className="text-gray-600 mb-4">
          Add your first item using the form on the left to get started.
        </p>
        <button
          onClick={fetchItems}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Refresh
        </button>
      </div>
    );
  }

  // Items grid
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <p className="text-sm text-gray-600">
          {items.length} item{items.length !== 1 ? 's' : ''} found
        </p>
        <button
          onClick={fetchItems}
          className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
        >
          Refresh
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {items.map((item) => (
          <ItemCard
            key={item.id}
            item={item}
            onItemDeleted={handleItemDeleted}
          />
        ))}
      </div>
    </div>
  );
};

export default ItemList;
