import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import ItemCard from './ItemCard';
import EditItemModal from './EditItemModal';
import LoadingSpinner, { CardSkeleton } from './LoadingSpinner';
import SearchAndFilter from './SearchAndFilter';

const ItemList = ({ refreshTrigger, onItemDeleted, viewMode = 'customer' }) => {
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingItem, setEditingItem] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Fetch items from API
  const fetchItems = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await axios.get('/api/items');
      
      if (response.data.success) {
        setItems(response.data.data);
        setFilteredItems(response.data.data);
      } else {
        setError('Failed to fetch items');
      }
    } catch (error) {
      console.error('Error fetching items:', error);
      setError(
        error.response?.data?.message || 
        error.response?.data?.error || 
        'Failed to load items. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch items on component mount and when refreshTrigger changes
  useEffect(() => {
    fetchItems();
  }, [refreshTrigger]);

  // Handle item deletion
  const handleItemDeleted = () => {
    fetchItems(); // Refresh the list
    if (onItemDeleted) {
      onItemDeleted();
    }
  };

  // Handle item edit
  const handleItemEdit = (item) => {
    setEditingItem(item);
    setIsEditModalOpen(true);
  };

  // Handle item update
  const handleItemUpdated = () => {
    fetchItems(); // Refresh the list
    setIsEditModalOpen(false);
    setEditingItem(null);
  };

  // Handle edit modal close
  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setEditingItem(null);
  };

  // Search functionality
  const handleSearch = (searchTerm) => {
    let filtered = items;

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredItems(filtered);
  };

  // Filter by price range
  const handleFilter = (priceRange) => {
    let filtered = items;

    if (priceRange !== 'all') {
      switch (priceRange) {
        case '0-50':
          filtered = filtered.filter(item => item.pricePerKg <= 50);
          break;
        case '50-100':
          filtered = filtered.filter(item => item.pricePerKg > 50 && item.pricePerKg <= 100);
          break;
        case '100-200':
          filtered = filtered.filter(item => item.pricePerKg > 100 && item.pricePerKg <= 200);
          break;
        case '200+':
          filtered = filtered.filter(item => item.pricePerKg > 200);
          break;
        default:
          break;
      }
    }

    setFilteredItems(filtered);
  };

  // Sort functionality
  const handleSort = (sortBy) => {
    let sorted = [...filteredItems];

    switch (sortBy) {
      case 'name':
        sorted.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name-desc':
        sorted.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'price-asc':
        sorted.sort((a, b) => a.pricePerKg - b.pricePerKg);
        break;
      case 'price-desc':
        sorted.sort((a, b) => b.pricePerKg - a.pricePerKg);
        break;
      default:
        break;
    }

    setFilteredItems(sorted);
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <LoadingSpinner size="large" text="Loading fresh products..." />
        <div className={`grid gap-6 ${
          viewMode === 'admin'
            ? 'grid-cols-1 lg:grid-cols-2'
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {[...Array(6)].map((_, index) => (
            <CardSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md inline-block">
          {error}
        </div>
        <div className="mt-4">
          <button
            onClick={fetchItems}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (filteredItems.length === 0 && !loading) {
    return (
      <motion.div
        className="text-center py-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <motion.div
          className="w-24 h-24 mx-auto mb-6 bg-gradient-beige rounded-3xl flex items-center justify-center"
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <svg className="w-12 h-12 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </motion.div>
        <h3 className="text-2xl font-bold text-gray-900 mb-3">No products yet</h3>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          {items.length === 0
            ? (viewMode === 'admin'
                ? "Start building your inventory by adding your first product using the form."
                : "Our fresh products will appear here soon. Check back later!")
            : "No products match your current filters. Try adjusting your search or filters."
          }
        </p>
        <motion.button
          onClick={fetchItems}
          className="px-6 py-3 bg-gradient-fresh text-white rounded-xl hover:shadow-fresh transition-all duration-300 font-semibold"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Refresh</span>
          </div>
        </motion.button>
      </motion.div>
    );
  }

  // Items grid
  return (
    <div className="space-y-6">
      {/* Search and Filter - Only show for customer view */}
      {viewMode === 'customer' && items.length > 0 && (
        <SearchAndFilter
          onSearch={handleSearch}
          onFilter={handleFilter}
          onSort={handleSort}
          totalItems={items.length}
        />
      )}

      <div className="flex justify-between items-center mb-4">
        <p className="text-sm text-gray-600">
          {filteredItems.length} item{filteredItems.length !== 1 ? 's' : ''} found
          {items.length !== filteredItems.length && ` (${items.length} total)`}
        </p>
        <button
          onClick={fetchItems}
          className="text-sm text-emerald-600 hover:text-emerald-800 transition-colors font-medium"
        >
          Refresh
        </button>
      </div>

      <div className={`grid gap-6 ${
        viewMode === 'admin'
          ? 'grid-cols-1 lg:grid-cols-2'
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      }`}>
        {filteredItems.map((item) => (
          <ItemCard
            key={item.id}
            item={item}
            onItemDeleted={handleItemDeleted}
            onItemEdit={handleItemEdit}
            viewMode={viewMode}
          />
        ))}
      </div>

      {/* Edit Modal */}
      <EditItemModal
        item={editingItem}
        isOpen={isEditModalOpen}
        onClose={handleEditModalClose}
        onItemUpdated={handleItemUpdated}
      />
    </div>
  );
};

export default ItemList;
