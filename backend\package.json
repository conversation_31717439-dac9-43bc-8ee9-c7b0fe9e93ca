{"name": "bauchar-groceries-backend", "version": "1.0.0", "description": "Backend API for Bauchar Groceries Store management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["bau<PERSON><PERSON>", "groceries", "store", "inventory", "calculator", "express", "mongodb"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.17.1", "multer": "^2.0.2"}, "devDependencies": {"nodemon": "^3.1.10"}}