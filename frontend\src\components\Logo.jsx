import React from 'react';

const Logo = ({ className = "h-10 w-auto", showText = true }) => {
  return (
    <div className="flex items-center space-x-3">
      {/* Logo SVG */}
      <svg 
        className={className} 
        viewBox="0 0 60 60" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background Circle */}
        <circle cx="30" cy="30" r="28" fill="#059669" stroke="#047857" strokeWidth="2"/>
        
        {/* Shopping Basket */}
        <path 
          d="M15 25h30l-3 15H18l-3-15z" 
          fill="#ffffff" 
          stroke="#047857" 
          strokeWidth="1.5"
        />
        
        {/* Basket Handle */}
        <path 
          d="M20 25v-5c0-5.5 4.5-10 10-10s10 4.5 10 10v5" 
          stroke="#ffffff" 
          strokeWidth="2" 
          fill="none"
        />
        
        {/* Vegetables/Fruits in basket */}
        <circle cx="22" cy="32" r="2.5" fill="#ef4444"/>
        <circle cx="30" cy="30" r="2" fill="#f59e0b"/>
        <circle cx="38" cy="33" r="2.5" fill="#10b981"/>
        
        {/* Store initial "B" */}
        <text 
          x="30" 
          y="50" 
          textAnchor="middle" 
          fill="#ffffff" 
          fontSize="16" 
          fontWeight="bold" 
          fontFamily="Arial, sans-serif"
        >
          B
        </text>
      </svg>
      
      {/* Store Name */}
      {showText && (
        <div className="flex flex-col">
          <span className="text-xl font-bold text-green-700">Bauchar</span>
          <span className="text-sm text-green-600 -mt-1">Groceries Store</span>
        </div>
      )}
    </div>
  );
};

export default Logo;
