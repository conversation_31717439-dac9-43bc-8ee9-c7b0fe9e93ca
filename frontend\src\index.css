@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Theme */
:root {
  --primary-green: #10b981;
  --primary-green-dark: #059669;
  --secondary-orange: #f97316;
  --secondary-orange-dark: #ea580c;
  --accent-beige: #fef3c7;
  --accent-beige-dark: #fde68a;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --background-light: #fefefe;
  --background-gradient: linear-gradient(135deg, #ecfdf5 0%, #fef3c7 100%);
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background-gradient);
  min-height: 100vh;
}

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Custom Utility Classes */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeIn {
  animation: fadeIn 0.8s ease-out;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

.animate-spin-slow {
  animation: spin 2s linear infinite;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient Backgrounds */
.bg-gradient-fresh {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.bg-gradient-orange {
  background: linear-gradient(135deg, #f97316 0%, #fb923c 100%);
}

.bg-gradient-beige {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.bg-gradient-main {
  background: linear-gradient(135deg, #ecfdf5 0%, #fef3c7 50%, #fef7ed 100%);
}

/* Custom Shadows */
.shadow-fresh {
  box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.1), 0 10px 10px -5px rgba(16, 185, 129, 0.04);
}

.shadow-orange {
  box-shadow: 0 10px 25px -5px rgba(249, 115, 22, 0.1), 0 10px 10px -5px rgba(249, 115, 22, 0.04);
}

.shadow-soft {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #10b981;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #059669;
}
