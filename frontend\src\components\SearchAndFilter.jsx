import React, { useState } from 'react';
import { motion } from 'framer-motion';

const SearchAndFilter = ({ onSearch, onFilter, onSort, totalItems }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [priceRange, setPriceRange] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  const handlePriceFilter = (range) => {
    setPriceRange(range);
    onFilter(range);
  };

  const handleSort = (sortOption) => {
    setSortBy(sortOption);
    onSort(sortOption);
  };

  const priceRanges = [
    { value: 'all', label: 'All Prices', count: totalItems },
    { value: '0-50', label: '₹0 - ₹50', count: 0 },
    { value: '50-100', label: '₹50 - ₹100', count: 0 },
    { value: '100-200', label: '₹100 - ₹200', count: 0 },
    { value: '200+', label: '₹200+', count: 0 }
  ];

  const sortOptions = [
    { value: 'name', label: 'Name (A-Z)' },
    { value: 'name-desc', label: 'Name (Z-A)' },
    { value: 'price-asc', label: 'Price (Low to High)' },
    { value: 'price-desc', label: 'Price (High to Low)' }
  ];

  return (
    <motion.div
      className="bg-white rounded-2xl shadow-card p-6 mb-8 border border-gray-100"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Search */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            Search Products
          </label>
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder="Search by name..."
              className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300"
            />
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Price Filter */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            Filter by Price
          </label>
          <div className="space-y-2">
            {priceRanges.map((range) => (
              <button
                key={range.value}
                onClick={() => handlePriceFilter(range.value)}
                className={`w-full text-left px-4 py-2 rounded-lg transition-all duration-300 ${
                  priceRange === range.value
                    ? 'bg-gradient-emerald text-white shadow-emerald'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{range.label}</span>
                  <span className="text-xs opacity-75">({range.count})</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Sort */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            Sort Products
          </label>
          <select
            value={sortBy}
            onChange={(e) => handleSort(e.target.value)}
            className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white transition-all duration-300"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Active Filters Display */}
      {(searchTerm || priceRange !== 'all' || sortBy !== 'name') && (
        <motion.div
          className="mt-6 pt-6 border-t border-gray-100"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium text-gray-600">Active filters:</span>
            
            {searchTerm && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                Search: "{searchTerm}"
                <button
                  onClick={() => {
                    setSearchTerm('');
                    onSearch('');
                  }}
                  className="ml-2 text-emerald-600 hover:text-emerald-800"
                >
                  ×
                </button>
              </span>
            )}
            
            {priceRange !== 'all' && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Price: {priceRanges.find(r => r.value === priceRange)?.label}
                <button
                  onClick={() => {
                    setPriceRange('all');
                    onFilter('all');
                  }}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}
            
            {sortBy !== 'name' && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Sort: {sortOptions.find(s => s.value === sortBy)?.label}
                <button
                  onClick={() => {
                    setSortBy('name');
                    onSort('name');
                  }}
                  className="ml-2 text-purple-600 hover:text-purple-800"
                >
                  ×
                </button>
              </span>
            )}
            
            <button
              onClick={() => {
                setSearchTerm('');
                setPriceRange('all');
                setSortBy('name');
                onSearch('');
                onFilter('all');
                onSort('name');
              }}
              className="text-xs text-gray-500 hover:text-gray-700 underline"
            >
              Clear all
            </button>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default SearchAndFilter;
