// Test script for the Shopkeeper Calculator API
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🧪 Testing Shopkeeper Calculator API...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data.message);
    console.log('');

    // Test 2: Get all items (should be empty initially)
    console.log('2. Testing GET /items...');
    const itemsResponse = await axios.get(`${BASE_URL}/items`);
    console.log('✅ GET /items passed:', itemsResponse.data);
    console.log('');

    // Test 3: Test calculator endpoints (will fail since no items exist yet)
    console.log('3. Testing calculator endpoints with non-existent item...');
    try {
      await axios.post(`${BASE_URL}/items/nonexistent/calculate-price`, { weight: 250 });
    } catch (error) {
      console.log('✅ Expected error for non-existent item:', error.response.data.error);
    }
    console.log('');

    console.log('🎉 Basic API tests completed successfully!');
    console.log('');
    console.log('📝 Manual testing steps:');
    console.log('1. Open http://localhost:3000 in your browser');
    console.log('2. Add a new item using the form:');
    console.log('   - Name: Apple');
    console.log('   - Price per Kg: 100');
    console.log('   - Upload an image file');
    console.log('3. Test the calculator:');
    console.log('   - Enter 250g → should show ₹25.00');
    console.log('   - Enter ₹120 → should show 1.20 Kg');
    console.log('4. Test deleting the item');
    console.log('');
    console.log('🚀 Both servers are running:');
    console.log('   - Backend: http://localhost:5000');
    console.log('   - Frontend: http://localhost:3000');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testAPI();
