// Test script for the Bauchar Groceries Store API
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🧪 Testing Bauchar Groceries Store API...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data.message);
    console.log('');

    // Test 2: Get all items (should be empty initially)
    console.log('2. Testing GET /items...');
    const itemsResponse = await axios.get(`${BASE_URL}/items`);
    console.log('✅ GET /items passed:', itemsResponse.data);
    console.log('');

    // Test 3: Test calculator endpoints (will fail since no items exist yet)
    console.log('3. Testing calculator endpoints with non-existent item...');
    try {
      await axios.post(`${BASE_URL}/items/nonexistent/calculate-price`, { weight: 250 });
    } catch (error) {
      console.log('✅ Expected error for non-existent item:', error.response.data.error);
    }
    console.log('');

    console.log('🎉 Basic API tests completed successfully!');
    console.log('');
    console.log('📝 Manual testing steps:');
    console.log('1. Open http://localhost:3000 in your browser');
    console.log('2. Switch to Admin Panel view');
    console.log('3. Add a new product using the form:');
    console.log('   - Name: Apple');
    console.log('   - Price per Kg: 100');
    console.log('   - Upload an image file');
    console.log('4. Test admin features:');
    console.log('   - Edit the product (click edit icon)');
    console.log('   - Delete the product (click delete icon)');
    console.log('5. Switch to Customer View and test calculator:');
    console.log('   - Enter 250g → should show ₹25.00');
    console.log('   - Enter ₹120 → should show 1.20 Kg');
    console.log('');
    console.log('🚀 Bauchar Groceries Store is running:');
    console.log('   - Backend API: http://localhost:5000');
    console.log('   - Frontend App: http://localhost:3000');
    console.log('   - Features: Customer View + Admin Panel');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testAPI();
