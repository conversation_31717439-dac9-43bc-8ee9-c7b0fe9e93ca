import React, { useState } from 'react';
import axios from 'axios';

const ItemCard = ({ item, onItemDeleted, viewMode = 'customer', onItemEdit }) => {
  const [weight, setWeight] = useState('');
  const [price, setPrice] = useState('');
  const [calculatedPrice, setCalculatedPrice] = useState('');
  const [calculatedWeight, setCalculatedWeight] = useState('');
  const [deleting, setDeleting] = useState(false);

  // Calculate price from weight
  const handleWeightChange = (e) => {
    const weightValue = e.target.value;
    setWeight(weightValue);
    
    if (weightValue && weightValue > 0) {
      const priceResult = (item.pricePerKg * weightValue) / 1000;
      setCalculatedPrice(priceResult.toFixed(2));
    } else {
      setCalculatedPrice('');
    }
    
    // Clear price input when weight is being calculated
    setPrice('');
    setCalculatedWeight('');
  };

  // Calculate weight from price
  const handlePriceChange = (e) => {
    const priceValue = e.target.value;
    setPrice(priceValue);
    
    if (priceValue && priceValue > 0) {
      const weightResult = (priceValue * 1000) / item.pricePerKg;
      setCalculatedWeight(weightResult.toFixed(2));
    } else {
      setCalculatedWeight('');
    }
    
    // Clear weight input when price is being calculated
    setWeight('');
    setCalculatedPrice('');
  };

  // Format weight display
  const formatWeight = (weightInGrams) => {
    const weight = parseFloat(weightInGrams);
    if (weight >= 1000) {
      return `${(weight / 1000).toFixed(2)} Kg`;
    }
    return `${weight.toFixed(0)} g`;
  };

  // Handle item deletion
  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete "${item.name}"?`)) {
      return;
    }

    setDeleting(true);
    try {
      const response = await axios.delete(`/api/items/${item.id}`);
      if (response.data.success) {
        if (onItemDeleted) {
          onItemDeleted();
        }
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('Failed to delete item. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      {/* Item Image */}
      <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl overflow-hidden">
        <img
          src={`http://localhost:5000${item.image}`}
          alt={item.name}
          className="w-full h-48 object-cover"
          onError={(e) => {
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDMTA4LjI4NCA3MCA5NS4wNzE2IDc2LjE5MjYgMTAwIDg0LjQ3NzZDMTA0LjkyOCA3Ni4xOTI2IDExMS43MTYgNzAgMTIwIDcwQzEyOC4yODQgNzAgMTM1IDc2LjcxNiAxMzUgODVDMTM1IDkzLjI4NCAxMjguMjg0IDEwMCAxMjAgMTAwSDgwQzcxLjcxNiAxMDAgNjUgOTMuMjg0IDY1IDg1QzY1IDc2LjcxNiA3MS43MTYgNzAgODAgNzBIMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
          }}
        />

        {/* Price Badge */}
        <div className="absolute top-3 right-3 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
          ₹{item.pricePerKg}/kg
        </div>
      </div>

      {/* Item Details */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 mb-1">{item.name}</h3>
            <p className="text-gray-600 text-sm">Fresh & Quality Guaranteed</p>
          </div>

          {/* Admin Controls */}
          {viewMode === 'admin' && (
            <div className="flex space-x-2">
              <button
                onClick={() => onItemEdit && onItemEdit(item)}
                className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors group"
                title="Edit item"
              >
                <svg className="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              <button
                onClick={handleDelete}
                disabled={deleting}
                className="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50 group"
                title="Delete item"
              >
                {deleting ? (
                  <div className="w-5 h-5 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg className="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                )}
              </button>
            </div>
          )}
        </div>

        {/* Calculator Section */}
        <div className="space-y-4 border-t border-gray-200 pt-4">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            <h4 className="text-lg font-semibold text-gray-800">Price Calculator</h4>
          </div>

          {/* Weight to Price */}
          <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
            <label className="block text-sm font-medium text-green-800 mb-2">
              Enter weight (grams):
            </label>
            <div className="flex space-x-3">
              <input
                type="number"
                value={weight}
                onChange={handleWeightChange}
                placeholder="e.g., 250"
                min="0"
                step="1"
                className="flex-1 px-4 py-3 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white"
              />
              <div className="flex items-center px-4 py-3 bg-green-600 text-white rounded-lg min-w-[100px] font-bold text-center">
                {calculatedPrice ? `₹${calculatedPrice}` : '₹0.00'}
              </div>
            </div>
          </div>

          {/* Price to Weight */}
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
            <label className="block text-sm font-medium text-blue-800 mb-2">
              Enter price (₹):
            </label>
            <div className="flex space-x-3">
              <input
                type="number"
                value={price}
                onChange={handlePriceChange}
                placeholder="e.g., 25"
                min="0"
                step="0.01"
                className="flex-1 px-4 py-3 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              />
              <div className="flex items-center px-4 py-3 bg-blue-600 text-white rounded-lg min-w-[100px] font-bold text-center">
                {calculatedWeight ? formatWeight(calculatedWeight) : '0 g'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemCard;
