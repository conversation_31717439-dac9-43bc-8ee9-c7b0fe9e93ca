import React, { useState } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';

const ItemCard = ({ item, onItemDeleted, viewMode = 'customer', onItemEdit }) => {
  const [weight, setWeight] = useState('');
  const [price, setPrice] = useState('');
  const [calculatedPrice, setCalculatedPrice] = useState('');
  const [calculatedWeight, setCalculatedWeight] = useState('');
  const [deleting, setDeleting] = useState(false);

  // Calculate price from weight
  const handleWeightChange = (e) => {
    const weightValue = e.target.value;
    setWeight(weightValue);
    
    if (weightValue && weightValue > 0) {
      const priceResult = (item.pricePerKg * weightValue) / 1000;
      setCalculatedPrice(priceResult.toFixed(2));
    } else {
      setCalculatedPrice('');
    }
    
    // Clear price input when weight is being calculated
    setPrice('');
    setCalculatedWeight('');
  };

  // Calculate weight from price
  const handlePriceChange = (e) => {
    const priceValue = e.target.value;
    setPrice(priceValue);
    
    if (priceValue && priceValue > 0) {
      const weightResult = (priceValue * 1000) / item.pricePerKg;
      setCalculatedWeight(weightResult.toFixed(2));
    } else {
      setCalculatedWeight('');
    }
    
    // Clear weight input when price is being calculated
    setWeight('');
    setCalculatedPrice('');
  };

  // Format weight display
  const formatWeight = (weightInGrams) => {
    const weight = parseFloat(weightInGrams);
    if (weight >= 1000) {
      return `${(weight / 1000).toFixed(2)} Kg`;
    }
    return `${weight.toFixed(0)} g`;
  };

  // Handle item deletion
  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete "${item.name}"?`)) {
      return;
    }

    setDeleting(true);
    try {
      const response = await axios.delete(`/api/items/${item.id}`);
      if (response.data.success) {
        if (onItemDeleted) {
          onItemDeleted();
        }
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('Failed to delete item. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-3xl shadow-soft hover-lift overflow-hidden group"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -8 }}
    >
      {/* Item Image */}
      <div className="relative bg-gradient-beige overflow-hidden">
        <motion.img
          src={`http://localhost:5000${item.image}`}
          alt={item.name}
          className="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110"
          onError={(e) => {
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDMTA4LjI4NCA3MCA5NS4wNzE2IDc2LjE5MjYgMTAwIDg0LjQ3NzZDMTA0LjkyOCA3Ni4xOTI2IDExMS43MTYgNzAgMTIwIDcwQzEyOC4yODQgNzAgMTM1IDc2LjcxNiAxMzUgODVDMTM1IDkzLjI4NCAxMjguMjg0IDEwMCAxMjAgMTAwSDgwQzcxLjcxNiAxMDAgNjUgOTMuMjg0IDY1IDg1QzY1IDc2LjcxNiA3MS43MTYgNzAgODAgNzBIMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
          }}
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.3 }}
        />

        {/* Price Badge */}
        <motion.div
          className="absolute top-4 right-4 bg-gradient-fresh text-white px-4 py-2 rounded-full text-sm font-bold shadow-fresh"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 500 }}
        >
          ₹{item.pricePerKg}/kg
        </motion.div>

        {/* Quality Badge */}
        <motion.div
          className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-green-700 px-3 py-1 rounded-full text-xs font-semibold"
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          Fresh
        </motion.div>
      </div>

      {/* Item Details */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div className="flex-1">
            <motion.h3
              className="text-2xl font-bold text-gray-900 mb-2 capitalize"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {item.name}
            </motion.h3>
            <motion.div
              className="flex items-center space-x-2 text-sm text-gray-600"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Fresh & Quality Guaranteed</span>
            </motion.div>
          </div>

          {/* Admin Controls */}
          {viewMode === 'admin' && (
            <motion.div
              className="flex space-x-2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 }}
            >
              <motion.button
                onClick={() => onItemEdit && onItemEdit(item)}
                className="p-3 rounded-xl bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 transition-all duration-300 group"
                title="Edit item"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-5 h-5 group-hover:rotate-12 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </motion.button>
              <motion.button
                onClick={handleDelete}
                disabled={deleting}
                className="p-3 rounded-xl bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 transition-all duration-300 disabled:opacity-50 group"
                title="Delete item"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                {deleting ? (
                  <div className="w-5 h-5 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg className="w-5 h-5 group-hover:rotate-12 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                )}
              </motion.button>
            </motion.div>
          )}
        </div>

        {/* Calculator Section */}
        <motion.div
          className="space-y-6 border-t border-gray-100 pt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <div className="flex items-center space-x-3">
            <motion.div
              className="w-10 h-10 bg-gradient-fresh rounded-xl flex items-center justify-center text-white"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </motion.div>
            <h4 className="text-xl font-bold text-gray-800">Smart Calculator</h4>
          </div>

          {/* Weight to Price */}
          <motion.div
            className="bg-gradient-to-r from-green-50 to-emerald-50 p-5 rounded-2xl border border-green-100"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <label className="block text-sm font-semibold text-green-800 mb-3 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-1m-3 1l-3-1" />
              </svg>
              Enter weight (grams):
            </label>
            <div className="flex space-x-3">
              <motion.input
                type="number"
                value={weight}
                onChange={handleWeightChange}
                placeholder="e.g., 250"
                min="0"
                step="1"
                className="flex-1 px-4 py-3 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white transition-all duration-300"
                whileFocus={{ scale: 1.02 }}
              />
              <motion.div
                className="flex items-center px-5 py-3 bg-gradient-fresh text-white rounded-xl min-w-[120px] font-bold text-center shadow-fresh"
                animate={{ scale: calculatedPrice ? [1, 1.05, 1] : 1 }}
                transition={{ duration: 0.3 }}
              >
                {calculatedPrice ? `₹${calculatedPrice}` : '₹0.00'}
              </motion.div>
            </div>
          </motion.div>

          {/* Price to Weight */}
          <motion.div
            className="bg-gradient-to-r from-orange-50 to-amber-50 p-5 rounded-2xl border border-orange-100"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <label className="block text-sm font-semibold text-orange-800 mb-3 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              Enter price (₹):
            </label>
            <div className="flex space-x-3">
              <motion.input
                type="number"
                value={price}
                onChange={handlePriceChange}
                placeholder="e.g., 25"
                min="0"
                step="0.01"
                className="flex-1 px-4 py-3 border-2 border-orange-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white transition-all duration-300"
                whileFocus={{ scale: 1.02 }}
              />
              <motion.div
                className="flex items-center px-5 py-3 bg-gradient-orange text-white rounded-xl min-w-[120px] font-bold text-center shadow-orange"
                animate={{ scale: calculatedWeight ? [1, 1.05, 1] : 1 }}
                transition={{ duration: 0.3 }}
              >
                {calculatedWeight ? formatWeight(calculatedWeight) : '0 g'}
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ItemCard;
