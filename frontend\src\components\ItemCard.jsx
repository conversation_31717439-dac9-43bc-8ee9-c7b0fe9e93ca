import React, { useState } from 'react';
import axios from 'axios';

const ItemCard = ({ item, onItemDeleted }) => {
  const [weight, setWeight] = useState('');
  const [price, setPrice] = useState('');
  const [calculatedPrice, setCalculatedPrice] = useState('');
  const [calculatedWeight, setCalculatedWeight] = useState('');
  const [deleting, setDeleting] = useState(false);

  // Calculate price from weight
  const handleWeightChange = (e) => {
    const weightValue = e.target.value;
    setWeight(weightValue);
    
    if (weightValue && weightValue > 0) {
      const priceResult = (item.pricePerKg * weightValue) / 1000;
      setCalculatedPrice(priceResult.toFixed(2));
    } else {
      setCalculatedPrice('');
    }
    
    // Clear price input when weight is being calculated
    setPrice('');
    setCalculatedWeight('');
  };

  // Calculate weight from price
  const handlePriceChange = (e) => {
    const priceValue = e.target.value;
    setPrice(priceValue);
    
    if (priceValue && priceValue > 0) {
      const weightResult = (priceValue * 1000) / item.pricePerKg;
      setCalculatedWeight(weightResult.toFixed(2));
    } else {
      setCalculatedWeight('');
    }
    
    // Clear weight input when price is being calculated
    setWeight('');
    setCalculatedPrice('');
  };

  // Format weight display
  const formatWeight = (weightInGrams) => {
    const weight = parseFloat(weightInGrams);
    if (weight >= 1000) {
      return `${(weight / 1000).toFixed(2)} Kg`;
    }
    return `${weight.toFixed(0)} g`;
  };

  // Handle item deletion
  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete "${item.name}"?`)) {
      return;
    }

    setDeleting(true);
    try {
      const response = await axios.delete(`/api/items/${item.id}`);
      if (response.data.success) {
        if (onItemDeleted) {
          onItemDeleted();
        }
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('Failed to delete item. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
      {/* Item Image */}
      <div className="aspect-w-16 aspect-h-9 bg-gray-100 rounded-t-lg overflow-hidden">
        <img
          src={`http://localhost:5000${item.image}`}
          alt={item.name}
          className="w-full h-48 object-cover"
          onError={(e) => {
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDMTA4LjI4NCA3MCA5NS4wNzE2IDc2LjE5MjYgMTAwIDg0LjQ3NzZDMTA0LjkyOCA3Ni4xOTI2IDExMS43MTYgNzAgMTIwIDcwQzEyOC4yODQgNzAgMTM1IDc2LjcxNiAxMzUgODVDMTM1IDkzLjI4NCAxMjguMjg0IDEwMCAxMjAgMTAwSDgwQzcxLjcxNiAxMDAgNjUgOTMuMjg0IDY1IDg1QzY1IDc2LjcxNiA3MS43MTYgNzAgODAgNzBIMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
          }}
        />
      </div>

      {/* Item Details */}
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
            <p className="text-sm text-gray-600">₹{item.pricePerKg}/kg</p>
          </div>
          <button
            onClick={handleDelete}
            disabled={deleting}
            className="text-red-600 hover:text-red-800 p-1 rounded transition-colors disabled:opacity-50"
            title="Delete item"
          >
            {deleting ? (
              <div className="w-5 h-5 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            )}
          </button>
        </div>

        {/* Calculator Section */}
        <div className="space-y-4 border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700">Calculator</h4>
          
          {/* Weight to Price */}
          <div className="space-y-2">
            <label className="block text-xs text-gray-600">Enter weight (grams):</label>
            <div className="flex space-x-2">
              <input
                type="number"
                value={weight}
                onChange={handleWeightChange}
                placeholder="e.g., 250"
                min="0"
                step="1"
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="flex items-center px-3 py-2 bg-gray-50 border border-gray-300 rounded-md min-w-[80px]">
                <span className="text-sm font-medium text-gray-900">
                  {calculatedPrice ? `₹${calculatedPrice}` : '₹0.00'}
                </span>
              </div>
            </div>
          </div>

          {/* Price to Weight */}
          <div className="space-y-2">
            <label className="block text-xs text-gray-600">Enter price (₹):</label>
            <div className="flex space-x-2">
              <input
                type="number"
                value={price}
                onChange={handlePriceChange}
                placeholder="e.g., 25"
                min="0"
                step="0.01"
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="flex items-center px-3 py-2 bg-gray-50 border border-gray-300 rounded-md min-w-[80px]">
                <span className="text-sm font-medium text-gray-900">
                  {calculatedWeight ? formatWeight(calculatedWeight) : '0 g'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemCard;
