import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const ItemCard = ({ item, onItemDeleted, viewMode = 'customer', onItemEdit }) => {
  const navigate = useNavigate();
  const [deleting, setDeleting] = useState(false);

  // Handle calculator click
  const handleCalculatorClick = () => {
    navigate(`/calculator/${item.id}`);
  };

  // Handle item deletion
  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete "${item.name}"?`)) {
      return;
    }

    setDeleting(true);
    try {
      const response = await axios.delete(`/api/items/${item.id}`);
      if (response.data.success) {
        if (onItemDeleted) {
          onItemDeleted();
        }
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('Failed to delete item. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-3xl shadow-card overflow-hidden border border-gray-100"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Item Image */}
      <div className="relative bg-gradient-to-br from-slate-50 to-slate-100 overflow-hidden">
        <img
          src={`http://localhost:5000${item.image}`}
          alt={item.name}
          className="w-full h-56 object-cover"
          onError={(e) => {
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDMTA4LjI4NCA3MCA5NS4wNzE2IDc2LjE5MjYgMTAwIDg0LjQ3NzZDMTA0LjkyOCA3Ni4xOTI2IDExMS43MTYgNzAgMTIwIDcwQzEyOC4yODQgNzAgMTM1IDc2LjcxNiAxMzUgODVDMTM1IDkzLjI4NCAxMjguMjg0IDEwMCAxMjAgMTAwSDgwQzcxLjcxNiAxMDAgNjUgOTMuMjg0IDY1IDg1QzY1IDc2LjcxNiA3MS43MTYgNzAgODAgNzBIMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
          }}
        />

        {/* Price Badge */}
        <div className="absolute top-4 right-4 bg-gradient-emerald text-white px-4 py-2 rounded-full text-sm font-bold shadow-emerald">
          ₹{item.pricePerKg}/kg
        </div>

        {/* Quality Badge */}
        <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm text-emerald-700 px-3 py-1 rounded-full text-xs font-semibold border border-emerald-200">
          Fresh Quality
        </div>
      </div>

      {/* Item Details */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div className="flex-1">
            <h3 className="text-2xl font-bold text-gray-900 mb-2 capitalize">
              {item.name}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Fresh & Quality Guaranteed</span>
            </div>
          </div>

          {/* Admin Controls */}
          {viewMode === 'admin' && (
            <div className="flex space-x-2">
              <button
                onClick={() => onItemEdit && onItemEdit(item)}
                className="p-3 rounded-xl bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 transition-all duration-300"
                title="Edit item"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              <button
                onClick={handleDelete}
                disabled={deleting}
                className="p-3 rounded-xl bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 transition-all duration-300 disabled:opacity-50"
                title="Delete item"
              >
                {deleting ? (
                  <div className="w-5 h-5 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                )}
              </button>
            </div>
          )}
        </div>

        {/* Calculator Button */}
        {viewMode === 'customer' && (
          <div className="border-t border-gray-100 pt-6">
            <button
              onClick={handleCalculatorClick}
              className="w-full bg-gradient-emerald text-white py-4 px-6 rounded-xl font-semibold text-lg hover:shadow-emerald transition-all duration-300 flex items-center justify-center space-x-3"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span>Open Price Calculator</span>
            </button>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ItemCard;
