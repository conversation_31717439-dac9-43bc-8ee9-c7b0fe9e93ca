import React, { useState } from 'react';
import axios from 'axios';

const ItemForm = ({ onItemAdded }) => {
  const [formData, setFormData] = useState({
    name: '',
    pricePerKg: '',
    image: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear messages when user starts typing
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({
      ...prev,
      image: file
    }));
    // Clear messages when user selects file
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name.trim()) {
      setError('Item name is required');
      return;
    }
    
    if (!formData.pricePerKg || formData.pricePerKg <= 0) {
      setError('Valid price per kg is required');
      return;
    }
    
    if (!formData.image) {
      setError('Item image is required');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const submitData = new FormData();
      submitData.append('name', formData.name.trim());
      submitData.append('pricePerKg', formData.pricePerKg);
      submitData.append('image', formData.image);

      const response = await axios.post('/api/items', submitData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setSuccess('Item added successfully!');
        setFormData({
          name: '',
          pricePerKg: '',
          image: null
        });
        // Reset file input
        const fileInput = document.getElementById('image-input');
        if (fileInput) fileInput.value = '';
        
        // Notify parent component
        if (onItemAdded) {
          onItemAdded();
        }
      }
    } catch (error) {
      console.error('Error adding item:', error);
      setError(
        error.response?.data?.message || 
        error.response?.data?.error || 
        'Failed to add item. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Item Name */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
          Item Name
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="e.g., Apple, Rice, Tomato"
          disabled={loading}
        />
      </div>

      {/* Price per Kg */}
      <div>
        <label htmlFor="pricePerKg" className="block text-sm font-medium text-gray-700 mb-1">
          Price per Kg (₹)
        </label>
        <input
          type="number"
          id="pricePerKg"
          name="pricePerKg"
          value={formData.pricePerKg}
          onChange={handleInputChange}
          min="0"
          step="0.01"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="e.g., 100.00"
          disabled={loading}
        />
      </div>

      {/* Image Upload */}
      <div>
        <label htmlFor="image-input" className="block text-sm font-medium text-gray-700 mb-1">
          Item Image
        </label>
        <input
          type="file"
          id="image-input"
          accept="image/*"
          onChange={handleFileChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          disabled={loading}
        />
        <p className="text-xs text-gray-500 mt-1">
          Supported formats: JPG, PNG, GIF, WebP (Max 5MB)
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
          {success}
        </div>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={loading}
        className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
          loading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
        } text-white`}
      >
        {loading ? 'Adding Item...' : 'Add Item'}
      </button>
    </form>
  );
};

export default ItemForm;
