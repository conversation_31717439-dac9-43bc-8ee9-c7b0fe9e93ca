# Shopkeeper Item Calculator Web App

A full-stack web application for shopkeepers to manage items and calculate prices/weights instantly.

## Features

### Backend (Node.js + Express + MongoDB)
- **API Endpoints:**
  - `POST /items` → Add new item (name, pricePerKg, image upload)
  - `GET /items` → Get all items
  - `DELETE /items/:id` → Delete item
  - `PUT /items/:id` → Update item
- **Item Schema:** name, pricePerKg, image
- **File Upload:** Multer for image handling
- **Database:** MongoDB with Mongoose

### Frontend (React.js)
- **Shopkeeper Dashboard:** Form to add items with image upload
- **Item Display:** Card layout with image, name, and price/kg
- **Dual Calculator:**
  - Enter weight in grams → calculate price instantly
  - Enter price in ₹ → calculate weight instantly
  - Smart display: < 1000g shows in grams, >= 1000g shows in Kg
- **Styling:** Tailwind CSS
- **API Integration:** Axios

## Project Structure

```
├── backend/          # Node.js Express server
│   ├── models/       # MongoDB schemas
│   ├── routes/       # API endpoints
│   ├── middleware/   # Multer config
│   ├── uploads/      # Image storage
│   └── server.js     # Main server file
├── frontend/         # React application
│   ├── src/
│   │   ├── components/
│   │   │   ├── ItemForm.jsx
│   │   │   ├── ItemCard.jsx
│   │   │   └── ItemList.jsx
│   │   └── App.jsx
│   └── public/
└── README.md
```

## Example Workflow
1. Shopkeeper adds Apple (₹100/kg + image)
2. Item appears in list with calculator
3. Enter 250g → shows ₹25.00
4. Enter ₹120 → shows 1.20 Kg

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Git

### Backend Setup
```bash
cd backend
npm install
npm run dev
```

The backend server will start on http://localhost:5000

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

The frontend will start on http://localhost:3000

### Environment Configuration
Create a `.env` file in the backend directory:
```
PORT=5000
MONGODB_URI=mongodb://localhost:27017/shopkeeper-calculator
NODE_ENV=development
```

### Testing
Run the API test script:
```bash
node test-api.js
```

### Troubleshooting

#### Tailwind CSS PostCSS Error
If you encounter a PostCSS error with Tailwind CSS, make sure you have the correct PostCSS plugin installed:
```bash
cd frontend
npm install @tailwindcss/postcss
```

#### MongoDB Connection Issues
- Ensure MongoDB is running locally on port 27017
- Or update the `MONGODB_URI` in `.env` to point to your MongoDB instance

#### Port Conflicts
- Backend runs on port 5000
- Frontend runs on port 3000
- Make sure these ports are available

## API Endpoints

### Items
- `GET /api/items` - Get all items
- `POST /api/items` - Add new item (with image upload)
- `PUT /api/items/:id` - Update item
- `DELETE /api/items/:id` - Delete item
- `POST /api/items/:id/calculate-price` - Calculate price from weight
- `POST /api/items/:id/calculate-weight` - Calculate weight from price

### Health Check
- `GET /api/health` - Server health status

## Features Implemented

### Backend Features ✅
- RESTful API with Express.js
- MongoDB integration with Mongoose
- File upload handling with Multer
- Image storage and serving
- Input validation and error handling
- CORS enabled for frontend integration

### Frontend Features ✅
- Responsive React application
- Item management (Add/Delete)
- Real-time calculator functionality
- Image upload and display
- Tailwind CSS styling
- Error handling and loading states

### Calculator Logic ✅
- Weight to Price: `price = (pricePerKg × weight) / 1000`
- Price to Weight: `weight = (price × 1000) / pricePerKg`
- Smart weight display (grams < 1000, Kg ≥ 1000)

## Technologies Used
- **Backend:** Node.js, Express.js, MongoDB, Mongoose, Multer
- **Frontend:** React.js, Vite, Axios, Tailwind CSS
- **Database:** MongoDB
- **Styling:** Tailwind CSS
- **Development:** Nodemon, Vite HMR
