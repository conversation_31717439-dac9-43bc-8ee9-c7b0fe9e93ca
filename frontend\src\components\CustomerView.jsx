import React from 'react';
import { motion } from 'framer-motion';
import ItemList from './ItemList';

const CustomerView = ({ refreshTrigger, onItemDeleted }) => {
  return (
    <motion.section
      className="py-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {/* Products Section */}
      <motion.div
        className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-soft p-8 border border-white/20"
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <div className="text-center mb-12">
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Our Fresh
            <span className="bg-gradient-fresh bg-clip-text text-transparent"> Products</span>
          </motion.h2>
          <motion.p
            className="text-xl text-gray-600 max-w-2xl mx-auto"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            Calculate prices instantly for all our quality products with our smart calculator
          </motion.p>
        </div>

        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <ItemList
            refreshTrigger={refreshTrigger}
            onItemDeleted={onItemDeleted}
            viewMode="customer"
          />
        </motion.div>
      </motion.div>
    </motion.section>
  );
};

export default CustomerView;
